(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';

            if (!currentSentence) {
              return { error: '未找到当前句子' };
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              oDocument.ReplaceCurrentSentence(currentSentence);
              return { error: '未找到当前段落' };
            }

            return targetPosition;
          }

          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return { error: '段落位置超出范围' };
          }

          var oParagraph = allParagraphs[currentPosition];
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() === 'numberingLevel') {
            var currentLevel = oNumberingLevel.GetLevelIndex();

            var previousNumberingLevel = null;

            var specifiedParagraph = allParagraphs[currentPosition - 1];
            var specifiedNumbering = specifiedParagraph.GetNumbering();
            if (specifiedNumbering && specifiedNumbering.GetClassType() === 'numberingLevel') {
              var specifiedLevel = specifiedNumbering.GetLevelIndex();
              if (specifiedLevel == currentLevel) previousNumberingLevel = specifiedNumbering;
            }

            if (previousNumberingLevel) {
              var specifiedNumberingObj = previousNumberingLevel.GetNumbering();
              var newPrevNumberingObj = specifiedNumberingObj.GetLevel(currentLevel);
              oParagraph.SetNumbering(newPrevNumberingObj);

              for (var j = currentPosition + 1; j < allParagraphs.length; j++) {
                var nextParagraph = allParagraphs[j];
                var nextNumbering = nextParagraph.GetNumbering();
                if (nextNumbering && nextNumbering.GetClassType() === 'numberingLevel') {
                  var nextLevel = nextNumbering.GetLevelIndex();
                  if (nextLevel <= currentLevel && nextParagraph.GetStyle().GetName() != oParagraph.GetStyle().GetName()) {
                    break;
                  }
                  if (nextLevel >= currentLevel) {
                    var newNextNumberingObj = specifiedNumberingObj.GetLevel(nextLevel);
                    nextParagraph.SetNumbering(newNextNumberingObj);
                  }
                }
              }
            }
            return;
          } else {
            return { error: '当前段落无编号信息！' };
          }
        } catch (e) {
          console.error('发生错误:', e);
          return { error: e.message };
        }
      },
      false,
      true,
      function (result) {
        if (result && result.error) {
          this.executeMethod('ShowError', [result.error]);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function () {};
})(window, undefined);
